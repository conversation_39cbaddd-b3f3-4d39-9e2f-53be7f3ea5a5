# LoveStory弹幕服务项目结构

## 📁 项目目录结构

```
lovestory-danmu-server/
├── 📄 核心文件
│   ├── Dockerfile                 # Docker镜像构建文件
│   ├── docker-compose.yml         # Docker Compose配置
│   ├── requirements.txt           # Python依赖包
│   └── .env.example              # 环境变量配置示例
│
├── 🚀 部署脚本
│   ├── deploy.sh                 # 一键部署脚本
│   ├── quick-build.sh            # 快速构建脚本
│   ├── exec.sh                   # 容器执行脚本
│   └── run.sh                    # 应用启动脚本
│
├── 📚 文档
│   ├── README.md                 # 项目说明文档
│   ├── QUICK_START.md            # 快速开始指南
│   ├── CONFIGURATION.md          # 配置管理指南
│   ├── PROJECT_STRUCTURE.md      # 项目结构说明（本文件）
│   └── LICENSE                   # 开源许可证
│
├── ⚙️ 配置目录
│   ├── config/
│   │   ├── config.yml            # 应用配置模板
│   │   └── logs/                 # 日志目录
│   └── data/                     # 数据目录
│
├── 💻 源代码
│   └── src/
│       ├── main.py               # 应用入口
│       ├── config.py             # 配置管理
│       ├── database.py           # 数据库连接
│       ├── models.py             # 数据模型
│       ├── crud.py               # 数据库操作
│       ├── security.py           # 安全认证
│       ├── dandan_api.py         # DanDan API兼容
│       ├── scraper_manager.py    # 爬虫管理
│       ├── task_manager.py       # 任务管理
│       ├── performance_manager.py # 性能管理
│       ├── webhook_manager.py    # Webhook管理
│       ├── scheduler.py          # 调度器
│       ├── log_manager.py        # 日志管理
│       ├── concurrency_controller.py # 并发控制
│       ├── api/                  # API路由
│       ├── scrapers/             # 爬虫模块
│       ├── jobs/                 # 后台任务
│       └── webhook/              # Webhook处理
│
└── 🌐 静态资源
    └── static/
        ├── index.html            # Web界面
        ├── css/                  # 样式文件
        ├── js/                   # JavaScript文件
        ├── logo.png              # Logo图片
        └── placeholder.png       # 占位图片
```

## 🔧 核心组件说明

### 应用核心
- **main.py**: FastAPI应用入口，定义路由和中间件
- **config.py**: 配置管理，支持环境变量和YAML配置
- **database.py**: 数据库连接池和初始化
- **models.py**: Pydantic数据模型定义
- **crud.py**: 数据库CRUD操作

### 业务逻辑
- **dandan_api.py**: DanDan Play API兼容层
- **scraper_manager.py**: 弹幕爬虫管理器
- **task_manager.py**: 异步任务管理
- **webhook_manager.py**: Webhook事件处理

### 系统管理
- **security.py**: JWT认证和权限管理
- **performance_manager.py**: 性能监控和优化
- **scheduler.py**: 定时任务调度
- **log_manager.py**: 日志管理和轮转

## 📦 部署文件说明

### Docker相关
- **Dockerfile**: 多阶段构建，优化镜像大小
- **docker-compose.yml**: 完整的服务编排配置
- **.env.example**: 环境变量配置模板

### 脚本工具
- **deploy.sh**: 一键部署脚本，自动处理环境配置
- **quick-build.sh**: 快速构建脚本，支持超时处理
- **run.sh**: 容器内应用启动脚本
- **exec.sh**: 容器执行脚本

## 🔒 配置管理

### 配置优先级
1. **环境变量** (最高优先级)
2. **.env文件** (中等优先级)
3. **config.yml** (默认配置)

### 环境变量格式
```bash
LOVESTORY_SECTION__KEY=value
```

示例：
```bash
LOVESTORY_SERVER__HOST=0.0.0.0
LOVESTORY_DATABASE__PASSWORD=your_password
LOVESTORY_JWT__SECRET_KEY=your_secret_key
```

## 🚀 快速部署

### 方法一：一键部署
```bash
./deploy.sh
```

### 方法二：手动部署
```bash
# 1. 配置环境变量
cp .env.example .env
nano .env

# 2. 启动服务
docker-compose up -d
```

### 方法三：快速构建
```bash
./quick-build.sh
```

## 📊 目录用途

| 目录 | 用途 | 是否持久化 |
|------|------|-----------|
| `config/` | 配置文件和日志 | ✅ 是 |
| `data/` | 应用数据 | ✅ 是 |
| `src/` | 源代码 | ❌ 否 |
| `static/` | 静态资源 | ❌ 否 |

## 🔍 开发指南

### 添加新功能
1. 在 `src/` 目录下创建相应模块
2. 在 `src/api/` 下添加API路由
3. 更新 `models.py` 添加数据模型
4. 在 `crud.py` 中添加数据库操作

### 配置新环境变量
1. 在 `src/config.py` 中添加配置项
2. 更新 `.env.example` 添加示例值
3. 在 `docker-compose.yml` 中添加环境变量

### 添加新的爬虫
1. 在 `src/scrapers/` 下创建爬虫模块
2. 在 `scraper_manager.py` 中注册爬虫
3. 添加相应的配置项

这个精简的项目结构专注于核心功能，移除了冗余文件，便于维护和部署。
