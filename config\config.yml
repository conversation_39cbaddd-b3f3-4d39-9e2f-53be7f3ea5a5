# LoveStory弹幕服务配置文件
#
# 宝塔面板部署配置
#
# 配置优先级：
# 1. 环境变量 (最高优先级)
# 2. .env 文件
# 3. 此配置文件 (最低优先级)
#
# 环境变量格式：LOVESTORY_SECTION__KEY=value
# 例如：LOVESTORY_SERVER__HOST=0.0.0.0
#       LOVESTORY_DATABASE__HOST=127.0.0.1
#       LOVESTORY_JWT__SECRET_KEY=your-secret-key

# 服务器配置
server:
  host: "0.0.0.0"  # 服务监听地址，建议保持0.0.0.0
  port: 7768       # 服务端口，可在宝塔面板中配置防火墙

# 数据库配置 - 请根据实际情况修改
database:
  host: "127.0.0.1"         # 数据库主机地址，本地MySQL使用127.0.0.1
  port: 3306                # 数据库端口
  user: "danmu_user"        # 数据库用户名，请在宝塔面板MySQL中创建
  password: "your_password" # 数据库密码，请设置强密码
  name: "danmu_db"          # 数据库名称，请在宝塔面板MySQL中创建
  # TLS/SSL配置 - 本地部署通常不需要
  ssl_enabled: false           # 是否启用SSL/TLS连接
  ssl_verify_cert: true        # 是否验证服务器证书
  ssl_ca_path: null           # CA证书文件路径
  ssl_cert_path: null         # 客户端证书文件路径
  ssl_key_path: null          # 客户端私钥文件路径

# JWT (JSON Web Token) 配置 - 安全相关，请务必修改
jwt:
  secret_key: "PLEASE-CHANGE-THIS-SECRET-KEY-FOR-PRODUCTION"  # JWT密钥，生产环境必须修改为随机字符串
  algorithm: "HS256"                                          # 加密算法
  access_token_expire_minutes: 1440                           # 令牌有效期（分钟，默认24小时）

# 管理员配置 - 首次启动时创建的管理员账户
admin:
  initial_user: "admin"       # 初始管理员用户名
  initial_password: null      # 初始管理员密码，留空则自动生成

# Bangumi OAuth 配置 - 可选，用于Bangumi数据源
bangumi:
  client_id: ""      # Bangumi客户端ID，可在Web界面配置
  client_secret: ""  # Bangumi客户端密钥，可在Web界面配置

# 豆瓣配置 - 可选，用于豆瓣数据源
douban:
  cookie: null  # 豆瓣Cookie，可在Web界面配置
