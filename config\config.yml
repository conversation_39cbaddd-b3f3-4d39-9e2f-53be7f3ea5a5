# LoveStory弹幕服务配置文件
#
# 注意：此文件仅包含默认配置模板
# 实际配置请通过环境变量或部署文件进行设置
#
# 环境变量格式：LOVESTORY_SECTION__KEY=value
# 例如：LOVESTORY_SERVER__HOST=0.0.0.0
#       LOVESTORY_DATABASE__HOST=your-db-host
#       LOVESTORY_JWT__SECRET_KEY=your-secret-key

# 服务器配置
server:
  host: "0.0.0.0"  # 服务监听地址，默认所有接口
  port: 7768       # 服务端口

# 数据库配置
database:
  host: "localhost"      # 数据库主机地址
  port: 3306            # 数据库端口
  user: "danmu_user"    # 数据库用户名
  password: "password"  # 数据库密码
  name: "danmu_db"      # 数据库名称
  # TLS/SSL配置
  ssl_enabled: false           # 是否启用SSL/TLS连接
  ssl_verify_cert: true        # 是否验证服务器证书
  ssl_ca_path: null           # CA证书文件路径
  ssl_cert_path: null         # 客户端证书文件路径
  ssl_key_path: null          # 客户端私钥文件路径

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "change-this-secret-key-in-production"  # JWT密钥，生产环境必须修改
  algorithm: "HS256"                                  # 加密算法
  access_token_expire_minutes: 1440                   # 令牌有效期（分钟）

# 管理员配置
admin:
  initial_user: null      # 初始管理员用户名
  initial_password: null  # 初始管理员密码

# Bangumi OAuth 配置
bangumi:
  client_id: ""      # Bangumi客户端ID
  client_secret: ""  # Bangumi客户端密钥

# 豆瓣配置
douban:
  cookie: null  # 豆瓣Cookie
