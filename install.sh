#!/bin/bash

# LoveStory弹幕服务一键安装脚本（宝塔面板版）
# 
# 使用说明：
# 1. 确保已安装宝塔面板
# 2. 确保已安装MySQL和Python项目管理器
# 3. 运行此脚本进行自动安装配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="LoveStory弹幕服务"
PROJECT_DIR="/www/wwwroot/danmu_server"
DB_NAME="danmu_db"
DB_USER="danmu_user"
SERVICE_PORT="7768"

# 打印横幅
print_banner() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║              LoveStory弹幕服务一键安装工具                    ║${NC}"
    echo -e "${BLUE}║                   宝塔面板部署版                             ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查系统环境
check_environment() {
    echo -e "${YELLOW}🔍 检查系统环境...${NC}"
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ 请使用root用户运行此脚本${NC}"
        exit 1
    fi
    
    # 检查宝塔面板
    if [ ! -f "/www/server/panel/BT-Panel" ]; then
        echo -e "${RED}❌ 未检测到宝塔面板，请先安装宝塔面板${NC}"
        echo "安装命令: wget -O install.sh http://download.bt.cn/install/install_6.0.sh && bash install.sh"
        exit 1
    fi
    echo -e "${GREEN}✅ 宝塔面板已安装${NC}"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3未安装，请在宝塔面板软件商店安装Python项目管理器${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Python3已安装${NC}"
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        echo -e "${RED}❌ MySQL未安装，请在宝塔面板软件商店安装MySQL${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ MySQL已安装${NC}"
}

# 生成随机密码
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-16
}

# 生成JWT密钥
generate_jwt_secret() {
    openssl rand -hex 32
}

# 创建项目目录
create_project_directory() {
    echo -e "${YELLOW}📁 创建项目目录...${NC}"
    
    if [ -d "$PROJECT_DIR" ]; then
        echo -e "${YELLOW}⚠️  项目目录已存在，是否覆盖？(y/N)${NC}"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            rm -rf "$PROJECT_DIR"
        else
            echo -e "${RED}❌ 安装取消${NC}"
            exit 1
        fi
    fi
    
    mkdir -p "$PROJECT_DIR"
    echo -e "${GREEN}✅ 项目目录创建完成${NC}"
}

# 复制项目文件
copy_project_files() {
    echo -e "${YELLOW}📋 复制项目文件...${NC}"
    
    # 获取当前脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制所有文件到项目目录
    cp -r "$SCRIPT_DIR"/* "$PROJECT_DIR/"
    
    # 设置权限
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    
    echo -e "${GREEN}✅ 项目文件复制完成${NC}"
}

# 配置数据库
setup_database() {
    echo -e "${YELLOW}🗄️  配置数据库...${NC}"
    
    # 生成数据库密码
    DB_PASSWORD=$(generate_password)
    
    # 获取MySQL root密码
    echo -e "${YELLOW}请输入MySQL root密码（在宝塔面板数据库页面可查看）:${NC}"
    read -s MYSQL_ROOT_PASSWORD
    
    # 创建数据库和用户
    mysql -uroot -p"$MYSQL_ROOT_PASSWORD" <<EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'127.0.0.1' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'127.0.0.1';
FLUSH PRIVILEGES;
EOF
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库配置完成${NC}"
        echo -e "${BLUE}数据库信息:${NC}"
        echo "  数据库名: $DB_NAME"
        echo "  用户名: $DB_USER"
        echo "  密码: $DB_PASSWORD"
    else
        echo -e "${RED}❌ 数据库配置失败${NC}"
        exit 1
    fi
}

# 配置环境变量
setup_environment() {
    echo -e "${YELLOW}⚙️  配置环境变量...${NC}"
    
    # 生成JWT密钥
    JWT_SECRET=$(generate_jwt_secret)
    
    # 创建.env文件
    cat > "$PROJECT_DIR/.env" <<EOF
# LoveStory弹幕服务环境配置（自动生成）
# 生成时间: $(date)

# ==================== 服务器配置 ====================
LOVESTORY_SERVER__HOST=0.0.0.0
LOVESTORY_SERVER__PORT=$SERVICE_PORT

# ==================== 数据库配置 ====================
LOVESTORY_DATABASE__HOST=127.0.0.1
LOVESTORY_DATABASE__PORT=3306
LOVESTORY_DATABASE__USER=$DB_USER
LOVESTORY_DATABASE__PASSWORD=$DB_PASSWORD
LOVESTORY_DATABASE__NAME=$DB_NAME
LOVESTORY_DATABASE__SSL_ENABLED=false

# ==================== JWT安全配置 ====================
LOVESTORY_JWT__SECRET_KEY=$JWT_SECRET
LOVESTORY_JWT__ALGORITHM=HS256
LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ==================== 管理员配置 ====================
LOVESTORY_ADMIN__INITIAL_USER=admin

# ==================== 其他配置 ====================
TZ=Asia/Shanghai
PYTHONPATH=$PROJECT_DIR
PYTHONUNBUFFERED=1
EOF
    
    # 设置文件权限
    chmod 600 "$PROJECT_DIR/.env"
    chown www:www "$PROJECT_DIR/.env"
    
    echo -e "${GREEN}✅ 环境变量配置完成${NC}"
}

# 安装Python依赖
install_dependencies() {
    echo -e "${YELLOW}📦 安装Python依赖...${NC}"
    
    cd "$PROJECT_DIR"
    
    # 升级pip
    python3 -m pip install --upgrade pip
    
    # 安装依赖
    python3 -m pip install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Python依赖安装完成${NC}"
    else
        echo -e "${RED}❌ Python依赖安装失败${NC}"
        exit 1
    fi
}

# 配置防火墙
setup_firewall() {
    echo -e "${YELLOW}🔥 配置防火墙...${NC}"
    
    # 检查宝塔面板防火墙配置
    if [ -f "/www/server/panel/data/port.pl" ]; then
        # 添加端口到宝塔面板防火墙
        echo "$SERVICE_PORT:tcp:LoveStory弹幕服务" >> /www/server/panel/data/port.pl
    fi
    
    # 系统防火墙配置
    if command -v ufw &> /dev/null; then
        ufw allow $SERVICE_PORT/tcp
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=$SERVICE_PORT/tcp
        firewall-cmd --reload
    fi
    
    echo -e "${GREEN}✅ 防火墙配置完成${NC}"
}

# 创建系统服务
create_systemd_service() {
    echo -e "${YELLOW}🔧 创建系统服务...${NC}"
    
    cat > /etc/systemd/system/lovestory-danmu.service <<EOF
[Unit]
Description=LoveStory弹幕服务
After=network.target mysql.service

[Service]
Type=exec
User=www
Group=www
WorkingDirectory=$PROJECT_DIR
Environment=PYTHONPATH=$PROJECT_DIR
Environment=PYTHONUNBUFFERED=1
ExecStart=/usr/bin/python3 -m src.main
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd配置
    systemctl daemon-reload
    systemctl enable lovestory-danmu.service
    
    echo -e "${GREEN}✅ 系统服务创建完成${NC}"
}

# 启动服务
start_service() {
    echo -e "${YELLOW}🚀 启动服务...${NC}"
    
    # 创建必要目录
    mkdir -p "$PROJECT_DIR/config/logs"
    mkdir -p "$PROJECT_DIR/data"
    chown -R www:www "$PROJECT_DIR"
    
    # 启动服务
    systemctl start lovestory-danmu.service
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if systemctl is-active --quiet lovestory-danmu.service; then
        echo -e "${GREEN}✅ 服务启动成功${NC}"
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        echo "查看日志: journalctl -u lovestory-danmu.service -f"
        exit 1
    fi
}

# 显示安装结果
show_result() {
    echo ""
    echo -e "${GREEN}🎉 安装完成！${NC}"
    echo ""
    echo -e "${BLUE}📋 服务信息:${NC}"
    echo "  项目目录: $PROJECT_DIR"
    echo "  服务端口: $SERVICE_PORT"
    echo "  数据库名: $DB_NAME"
    echo "  数据库用户: $DB_USER"
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  Web界面: http://$(hostname -I | awk '{print $1}'):$SERVICE_PORT"
    echo "  API文档: http://$(hostname -I | awk '{print $1}'):$SERVICE_PORT/docs"
    echo "  本地访问: http://localhost:$SERVICE_PORT"
    echo ""
    echo -e "${BLUE}👤 管理员账户:${NC}"
    echo "  用户名: admin"
    echo "  密码: 查看启动日志获取自动生成的密码"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo "  启动服务: systemctl start lovestory-danmu.service"
    echo "  停止服务: systemctl stop lovestory-danmu.service"
    echo "  重启服务: systemctl restart lovestory-danmu.service"
    echo "  查看状态: systemctl status lovestory-danmu.service"
    echo "  查看日志: journalctl -u lovestory-danmu.service -f"
    echo ""
    echo -e "${YELLOW}💡 重要提示:${NC}"
    echo "1. 首次登录后请立即修改管理员密码"
    echo "2. 请妥善保管数据库密码和JWT密钥"
    echo "3. 建议定期备份数据库和配置文件"
    echo "4. 生产环境建议配置HTTPS和域名"
}

# 主函数
main() {
    print_banner
    
    echo -e "${YELLOW}开始安装 $PROJECT_NAME...${NC}"
    echo ""
    
    check_environment
    create_project_directory
    copy_project_files
    setup_database
    setup_environment
    install_dependencies
    setup_firewall
    create_systemd_service
    start_service
    show_result
    
    echo ""
    echo -e "${GREEN}🚀 $PROJECT_NAME 安装完成！${NC}"
}

# 运行主函数
main "$@"
