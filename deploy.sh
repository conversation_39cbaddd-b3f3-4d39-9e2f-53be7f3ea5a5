#!/bin/bash

# LoveStory弹幕服务一键部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印横幅
print_banner() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║              LoveStory弹幕服务一键部署工具                    ║${NC}"
    echo -e "${BLUE}║              LoveStory Danmu Server Deploy Tool              ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查Docker和Docker Compose
check_requirements() {
    echo -e "${YELLOW}🔍 检查系统要求...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
        echo "安装命令："
        echo "  CentOS/RHEL: yum install -y docker"
        echo "  Ubuntu/Debian: apt-get install -y docker.io"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker已安装${NC}"
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
        echo "安装命令："
        echo "  curl -L \"https://github.com/docker/compose/releases/download/1.29.2/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
        echo "  chmod +x /usr/local/bin/docker-compose"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker Compose已安装${NC}"
    
    if ! systemctl is-active --quiet docker; then
        echo -e "${YELLOW}⚠️  Docker服务未运行，正在启动...${NC}"
        systemctl start docker
        systemctl enable docker
    fi
    echo -e "${GREEN}✅ Docker服务正在运行${NC}"
}

# 创建必要的目录和配置文件
create_directories() {
    echo -e "${YELLOW}📁 创建必要目录和配置文件...${NC}"
    mkdir -p config/logs
    mkdir -p data

    # 检查是否存在环境变量文件
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}⚠️  未找到 .env 文件${NC}"
        if [ -f ".env.example" ]; then
            cp .env.example .env
            echo -e "${YELLOW}⚠️  已复制 .env.example 为 .env，请手动编辑配置${NC}"
            echo "编辑命令: nano .env 或 vim .env"
            echo "请设置数据库连接、JWT密钥等重要配置"
            read -p "按回车键继续..."
        else
            echo -e "${RED}❌ .env.example 文件不存在${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ 找到现有的 .env 文件${NC}"
    fi

    echo -e "${GREEN}✅ 目录和配置文件准备完成${NC}"
}

# 停止现有服务
stop_existing_service() {
    echo -e "${YELLOW}🛑 停止现有服务...${NC}"
    
    # 尝试停止可能存在的容器
    if docker ps -q --filter "name=lovestory-danmu-server" | grep -q .; then
        echo "停止现有容器..."
        docker stop lovestory-danmu-server || true
        docker rm lovestory-danmu-server || true
    fi
    
    # 使用docker-compose停止
    if [ -f "docker-compose.yml" ]; then
        docker-compose down || true
    fi
    
    echo -e "${GREEN}✅ 现有服务已停止${NC}"
}

# 构建和启动服务
build_and_start() {
    echo -e "${YELLOW}🔨 构建并启动服务...${NC}"

    # 使用默认compose文件
    COMPOSE_FILE="docker-compose.yml"
    echo "使用配置文件: $COMPOSE_FILE"

    # 构建并启动
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d --build
    else
        docker compose up -d --build
    fi
    
    echo -e "${GREEN}✅ 服务启动成功${NC}"
}

# 等待服务就绪
wait_for_service() {
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:7768/docs > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 服务已就绪${NC}"
            return 0
        fi
        
        echo "尝试 $attempt/$max_attempts - 等待服务启动..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "${YELLOW}⚠️  服务启动可能需要更多时间，请稍后手动检查${NC}"
}

# 显示服务信息
show_service_info() {
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo ""
    echo -e "${BLUE}📋 服务信息:${NC}"
    echo "  容器名称: lovestory-danmu-server"
    echo "  服务端口: 7768"
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  Web界面: http://$(hostname -I | awk '{print $1}'):7768"
    echo "  API文档: http://$(hostname -I | awk '{print $1}'):7768/docs"
    echo "  本地访问: http://localhost:7768"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo "  查看日志: docker logs -f lovestory-danmu-server"
    echo "  重启服务: docker restart lovestory-danmu-server"
    echo "  停止服务: docker stop lovestory-danmu-server"
    echo "  进入容器: docker exec -it lovestory-danmu-server /bin/sh"
    echo ""
    echo -e "${BLUE}📊 服务状态:${NC}"
    docker ps --filter "name=lovestory-danmu-server" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 主函数
main() {
    print_banner
    
    # 解析命令行参数
    SKIP_CHECKS=false
    COMPOSE_FILE=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-checks)
                SKIP_CHECKS=true
                shift
                ;;
            -f|--file)
                COMPOSE_FILE="$2"
                shift 2
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-checks     跳过系统要求检查"
                echo "  -f, --file FILE   指定docker-compose文件"
                echo "  -h, --help        显示帮助信息"
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                exit 1
                ;;
        esac
    done
    
    if [ "$SKIP_CHECKS" != "true" ]; then
        check_requirements
    fi
    
    create_directories
    stop_existing_service
    build_and_start
    wait_for_service
    show_service_info
    
    echo ""
    echo -e "${GREEN}🚀 LoveStory弹幕服务部署完成！${NC}"
}

# 运行主函数
main "$@"
