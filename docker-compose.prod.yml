version: '3.8'

services:
  lovestory-danmu:
    # 使用本地构建的镜像
    build:
      context: .
      dockerfile: Dockerfile
    # 如果您已经构建了本地镜像，也可以直接使用：
    # image: lovestory-danmu-server:latest
    container_name: lovestory-danmu-server
    restart: unless-stopped
    
    # 使用主机网络模式，容器将直接使用宿主机的网络
    # 这意味着容器内的 127.0.0.1 就是宿主机的 127.0.0.1
    # 应用将直接在宿主机的 7768 端口上可用，无需端口映射
    network_mode: "host"
    
    environment:
      # 容器权限配置
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - UMASK=${UMASK:-0022}

      # 服务配置
      - LOVESTORY_SERVER__HOST=${LOVESTORY_SERVER__HOST:-0.0.0.0}
      - LOVESTORY_SERVER__PORT=${LOVESTORY_SERVER__PORT:-7768}

      # 数据库配置 - 从环境变量读取
      - LOVESTORY_DATABASE__HOST=${LOVESTORY_DATABASE__HOST}
      - LOVESTORY_DATABASE__PORT=${LOVESTORY_DATABASE__PORT}
      - LOVESTORY_DATABASE__USER=${LOVESTORY_DATABASE__USER}
      - LOVESTORY_DATABASE__PASSWORD=${LOVESTORY_DATABASE__PASSWORD}
      - LOVESTORY_DATABASE__NAME=${LOVESTORY_DATABASE__NAME}
      - LOVESTORY_DATABASE__SSL_ENABLED=${LOVESTORY_DATABASE__SSL_ENABLED:-true}
      - LOVESTORY_DATABASE__SSL_VERIFY_CERT=${LOVESTORY_DATABASE__SSL_VERIFY_CERT:-true}
      - LOVESTORY_DATABASE__SSL_CA_PATH=${LOVESTORY_DATABASE__SSL_CA_PATH:-/etc/pki/tls/certs/ca-bundle.crt}

      # JWT 配置 - 生产环境必须设置强密钥
      - LOVESTORY_JWT__SECRET_KEY=${LOVESTORY_JWT__SECRET_KEY}
      - LOVESTORY_JWT__ALGORITHM=${LOVESTORY_JWT__ALGORITHM:-HS256}
      - LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=${LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES:-1440}

      # 管理员配置
      - LOVESTORY_ADMIN__INITIAL_USER=${LOVESTORY_ADMIN__INITIAL_USER:-admin}
      - LOVESTORY_ADMIN__INITIAL_PASSWORD=${LOVESTORY_ADMIN__INITIAL_PASSWORD:-}

      # Bangumi OAuth 配置
      - LOVESTORY_BANGUMI__CLIENT_ID=${LOVESTORY_BANGUMI__CLIENT_ID}
      - LOVESTORY_BANGUMI__CLIENT_SECRET=${LOVESTORY_BANGUMI__CLIENT_SECRET}

      # 豆瓣配置
      - LOVESTORY_DOUBAN__COOKIE=${LOVESTORY_DOUBAN__COOKIE:-}
      
    volumes:
      # 挂载 config 目录以持久化日志和配置文件
      - ./config:/app/config
      # 可选：挂载数据目录
      - ./data:/app/data
      
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7768/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
      
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
