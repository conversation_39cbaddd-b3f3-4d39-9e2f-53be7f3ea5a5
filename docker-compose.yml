version: '3.8'

services:
  app:
    # 使用本地 Dockerfile 构建镜像
    build:
      context: . # Dockerfile 将创建一个默认用户，权限由 entrypoint.sh 在运行时处理。
    # image: lovestory-danmu-server:latest # 如果您想使用预构建的镜像，请取消注释此行并注释掉上面的 'build: .'
    container_name: lovestory-danmu-server
    restart: unless-stopped
    # 使用主机网络模式，容器将直接使用宿主机的网络。
    # 这意味着容器内的 127.0.0.1 就是宿主机的 127.0.0.1。
    # 应用将直接在宿主机的 7768 端口上可用，无需端口映射。
    network_mode: "host"
    environment:
      # 容器权限配置
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - UMASK=${UMASK:-0022}

      # 服务配置
      - LOVESTORY_SERVER__HOST=${LOVESTORY_SERVER__HOST:-0.0.0.0}
      - LOVESTORY_SERVER__PORT=${LOVESTORY_SERVER__PORT:-7768}

      # 数据库配置
      - LOVESTORY_DATABASE__HOST=${LOVESTORY_DATABASE__HOST:-127.0.0.1}
      - LOVESTORY_DATABASE__PORT=${LOVESTORY_DATABASE__PORT:-3306}
      - LOVESTORY_DATABASE__USER=${LOVESTORY_DATABASE__USER:-danmu_user}
      - LOVESTORY_DATABASE__PASSWORD=${LOVESTORY_DATABASE__PASSWORD:-password}
      - LOVESTORY_DATABASE__NAME=${LOVESTORY_DATABASE__NAME:-danmu_db}
      - LOVESTORY_DATABASE__SSL_ENABLED=${LOVESTORY_DATABASE__SSL_ENABLED:-false}
      - LOVESTORY_DATABASE__SSL_VERIFY_CERT=${LOVESTORY_DATABASE__SSL_VERIFY_CERT:-true}
      - LOVESTORY_DATABASE__SSL_CA_PATH=${LOVESTORY_DATABASE__SSL_CA_PATH:-}

      # JWT 配置
      - LOVESTORY_JWT__SECRET_KEY=${LOVESTORY_JWT__SECRET_KEY:-change-this-secret-key-in-production}
      - LOVESTORY_JWT__ALGORITHM=${LOVESTORY_JWT__ALGORITHM:-HS256}
      - LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=${LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES:-1440}

      # 管理员配置
      - LOVESTORY_ADMIN__INITIAL_USER=${LOVESTORY_ADMIN__INITIAL_USER:-admin}
      - LOVESTORY_ADMIN__INITIAL_PASSWORD=${LOVESTORY_ADMIN__INITIAL_PASSWORD:-}

      # Bangumi OAuth 配置
      - LOVESTORY_BANGUMI__CLIENT_ID=${LOVESTORY_BANGUMI__CLIENT_ID:-bgm4222688b7532ef439}
      - LOVESTORY_BANGUMI__CLIENT_SECRET=${LOVESTORY_BANGUMI__CLIENT_SECRET:-379c426b8f26b561642334445761361f}

      # 豆瓣配置
      - LOVESTORY_DOUBAN__COOKIE=${LOVESTORY_DOUBAN__COOKIE:-}
    volumes:
      # 挂载 config 目录以持久化日志和配置文件。
      - ./config:/app/config

# 由于不再使用容器化的数据库，db 服务、volumes 和 networks 部分已被移除。
# 请确保您的宿主机上已经运行了 MySQL 服务，并且：
# 1. 监听在 127.0.0.1:3306。
# 2. 创建了名为 'danmuapi' 的数据库。
# 3. 创建了用户 'danmuapi'，密码为 'danmuapi'，并授予了对 'danmuapi' 数据库的访问权限。
