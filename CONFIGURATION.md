# LoveStory弹幕服务配置指南

## 📋 配置概述

LoveStory弹幕服务采用**配置与代码分离**的设计原则，所有敏感配置都通过环境变量进行管理，确保安全性和灵活性。

## 🔧 配置方式

### 1. 环境变量优先级

配置加载优先级（从高到低）：
1. **环境变量** - 最高优先级
2. **.env 文件** - 中等优先级  
3. **config.yml** - 默认配置模板

### 2. 环境变量命名规则

格式：`LOVESTORY_SECTION__KEY=value`

示例：
- `LOVESTORY_SERVER__HOST=0.0.0.0`
- `LOVESTORY_DATABASE__PASSWORD=your_password`
- `LOVESTORY_JWT__SECRET_KEY=your_secret_key`

## 📁 配置文件说明

### 核心配置文件

| 文件 | 用途 | 是否提交到Git |
|------|------|---------------|
| `config/config.yml` | 默认配置模板 | ✅ 是 |
| `.env.example` | 环境变量示例 | ✅ 是 |
| `.env.development` | 开发环境配置 | ✅ 是 |
| `.env.production` | 生产环境配置 | ❌ 否 |
| `.env` | 实际使用的配置 | ❌ 否 |

### 部署配置文件

| 文件 | 用途 |
|------|------|
| `docker-compose.yml` | 默认Docker部署配置 |
| `docker-compose.local.yml` | 本地开发部署配置 |
| `docker-compose.prod.yml` | 生产环境部署配置 |

## 🚀 快速配置

### 方法一：使用预设环境配置

```bash
# 开发环境
cp .env.development .env

# 生产环境
cp .env.production .env
# 然后编辑 .env 文件，修改敏感信息
```

### 方法二：从示例创建

```bash
# 复制示例文件
cp .env.example .env

# 编辑配置
nano .env  # 或使用其他编辑器
```

### 方法三：使用部署脚本

```bash
# 运行部署脚本，会自动引导配置选择
./deploy.sh
```

## ⚙️ 配置项详解

### 服务器配置

```bash
LOVESTORY_SERVER__HOST=0.0.0.0        # 服务监听地址
LOVESTORY_SERVER__PORT=7768           # 服务端口
```

### 数据库配置

#### 本地MySQL
```bash
LOVESTORY_DATABASE__HOST=localhost
LOVESTORY_DATABASE__PORT=3306
LOVESTORY_DATABASE__USER=danmu_user
LOVESTORY_DATABASE__PASSWORD=your_password
LOVESTORY_DATABASE__NAME=danmu_db
LOVESTORY_DATABASE__SSL_ENABLED=false
```

#### TiDB Cloud
```bash
LOVESTORY_DATABASE__HOST=your-host.tidbcloud.com
LOVESTORY_DATABASE__PORT=4000
LOVESTORY_DATABASE__USER=your_user
LOVESTORY_DATABASE__PASSWORD=your_password
LOVESTORY_DATABASE__NAME=your_db
LOVESTORY_DATABASE__SSL_ENABLED=true
LOVESTORY_DATABASE__SSL_VERIFY_CERT=true
LOVESTORY_DATABASE__SSL_CA_PATH=/etc/pki/tls/certs/ca-bundle.crt
```

### JWT配置

```bash
# 生产环境必须使用强密钥！
LOVESTORY_JWT__SECRET_KEY=your_strong_secret_key
LOVESTORY_JWT__ALGORITHM=HS256
LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=1440
```

生成强密钥：
```bash
openssl rand -base64 32
```

### 管理员配置

```bash
LOVESTORY_ADMIN__INITIAL_USER=admin
LOVESTORY_ADMIN__INITIAL_PASSWORD=your_admin_password
```

### 第三方服务配置

```bash
# Bangumi OAuth
LOVESTORY_BANGUMI__CLIENT_ID=your_client_id
LOVESTORY_BANGUMI__CLIENT_SECRET=your_client_secret

# 豆瓣
LOVESTORY_DOUBAN__COOKIE=your_douban_cookie
```

## 🔒 安全最佳实践

### 1. 敏感信息保护

- ❌ **不要**将包含敏感信息的 `.env` 文件提交到Git
- ✅ **使用**强密码和随机生成的密钥
- ✅ **定期**更换密钥和密码

### 2. 环境隔离

- 开发环境使用 `.env.development`
- 生产环境使用 `.env.production`
- 测试环境单独配置

### 3. 权限控制

```bash
# 设置配置文件权限
chmod 600 .env
chmod 600 .env.production
```

## 🌍 多环境部署

### 开发环境

```bash
# 使用开发配置
cp .env.development .env
docker-compose -f docker-compose.local.yml up -d
```

### 生产环境

```bash
# 使用生产配置
cp .env.production .env
# 编辑敏感信息
nano .env
# 部署
docker-compose -f docker-compose.prod.yml up -d
```

### 自定义环境

```bash
# 创建自定义环境配置
cp .env.example .env.staging
# 编辑配置
nano .env.staging
# 使用配置
cp .env.staging .env
```

## 🔍 配置验证

### 检查配置是否生效

```bash
# 查看容器环境变量
docker exec lovestory-danmu-server env | grep LOVESTORY

# 检查配置加载
docker logs lovestory-danmu-server | grep -i config
```

### 常见配置问题

1. **数据库连接失败**
   - 检查数据库配置是否正确
   - 验证网络连通性
   - 确认SSL配置

2. **JWT认证失败**
   - 确认密钥设置正确
   - 检查密钥长度和复杂度

3. **权限问题**
   - 检查PUID/PGID设置
   - 验证文件权限

## 📚 配置示例

### 完整的生产环境配置示例

```bash
# 容器权限
PUID=1000
PGID=1000
UMASK=0022

# 服务配置
LOVESTORY_SERVER__HOST=0.0.0.0
LOVESTORY_SERVER__PORT=7768

# 数据库配置
LOVESTORY_DATABASE__HOST=your-db-host.com
LOVESTORY_DATABASE__PORT=4000
LOVESTORY_DATABASE__USER=your_user
LOVESTORY_DATABASE__PASSWORD=your_strong_password
LOVESTORY_DATABASE__NAME=your_database
LOVESTORY_DATABASE__SSL_ENABLED=true
LOVESTORY_DATABASE__SSL_VERIFY_CERT=true

# JWT配置
LOVESTORY_JWT__SECRET_KEY=your_very_strong_secret_key_here
LOVESTORY_JWT__ALGORITHM=HS256
LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 管理员配置
LOVESTORY_ADMIN__INITIAL_USER=admin
LOVESTORY_ADMIN__INITIAL_PASSWORD=your_admin_password

# 第三方服务
LOVESTORY_BANGUMI__CLIENT_ID=your_bangumi_client_id
LOVESTORY_BANGUMI__CLIENT_SECRET=your_bangumi_client_secret
```

## 🆘 故障排查

如果遇到配置相关问题：

1. **检查环境变量**：`docker exec container_name env`
2. **查看应用日志**：`docker logs container_name`
3. **验证配置文件**：确认 `.env` 文件格式正确
4. **重启服务**：`docker-compose restart`

更多帮助请参考 `DOCKER_DEPLOYMENT.md` 和 `QUICK_DEPLOY.md`。
