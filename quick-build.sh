#!/bin/bash

# LoveStory弹幕服务快速构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 LoveStory弹幕服务快速构建工具${NC}"
echo ""

# 停止现有构建和容器
echo -e "${YELLOW}🛑 停止现有构建和容器...${NC}"
docker-compose -f docker-compose.local.yml down || true
docker system prune -f

# 使用默认Dockerfile构建
echo -e "${GREEN}✅ 使用默认Dockerfile构建${NC}"
BUILD_METHOD="default"

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  未找到 .env 文件，创建默认配置...${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${YELLOW}⚠️  已复制示例配置，请编辑 .env 文件设置数据库等配置${NC}"
        echo "编辑命令: nano .env 或 vim .env"
        read -p "按回车键继续..."
    else
        echo -e "${RED}❌ 未找到 .env.example 文件${NC}"
        exit 1
    fi
fi

# 开始构建
echo -e "${BLUE}🔨 开始构建...${NC}"
echo "构建方式: $BUILD_METHOD"
echo ""

# 构建并启动
echo -e "${BLUE}🚀 构建并启动服务...${NC}"
timeout 600 docker-compose up -d --build || {
    echo -e "${RED}❌ 构建超时或失败${NC}"
    echo -e "${YELLOW}💡 建议尝试以下解决方案：${NC}"
    echo "1. 检查网络连接"
    echo "2. 使用国内Docker镜像源"
    echo "3. 清理Docker缓存: docker system prune -a"
    echo "4. 重新运行此脚本选择不同的构建方式"
    exit 1
}

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
if docker ps | grep -q "lovestory-danmu-server"; then
    echo -e "${GREEN}✅ 服务启动成功！${NC}"
    echo ""
    echo -e "${BLUE}📋 服务信息:${NC}"
    docker ps --filter "name=lovestory-danmu-server" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  Web界面: http://localhost:7768"
    echo "  API文档: http://localhost:7768/docs"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo "  查看日志: docker logs -f lovestory-danmu-server"
    echo "  重启服务: docker restart lovestory-danmu-server"
    echo "  停止服务: docker stop lovestory-danmu-server"
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo -e "${YELLOW}查看日志:${NC}"
    docker-compose -f docker-compose.local.yml logs
fi

# 构建完成

echo -e "${GREEN}🎉 构建完成！${NC}"
