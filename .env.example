# LoveStory弹幕服务环境配置文件模板（宝塔面板部署版）
#
# 使用说明：
# 1. 复制此文件为 .env
# 2. 根据实际情况修改下面的配置
# 3. 重启应用使配置生效
#
# 注意：环境变量的优先级高于config.yml中的配置

# ==================== 服务器配置 ====================
# 服务监听地址，建议保持0.0.0.0
LOVESTORY_SERVER__HOST=0.0.0.0
# 服务端口，确保在宝塔面板防火墙中开放此端口
LOVESTORY_SERVER__PORT=7768

# ==================== 数据库配置 ====================
# 本地MySQL配置（推荐用于宝塔面板部署）
LOVESTORY_DATABASE__HOST=127.0.0.1
LOVESTORY_DATABASE__PORT=3306
LOVESTORY_DATABASE__USER=danmu_user
LOVESTORY_DATABASE__PASSWORD=change_me_to_a_strong_password
LOVESTORY_DATABASE__NAME=danmu_db
LOVESTORY_DATABASE__SSL_ENABLED=false

# 数据库SSL配置（本地部署通常不需要修改）
# LOVESTORY_DATABASE__SSL_VERIFY_CERT=true
# LOVESTORY_DATABASE__SSL_CA_PATH=/path/to/ca.pem
# LOVESTORY_DATABASE__SSL_CERT_PATH=/path/to/client-cert.pem
# LOVESTORY_DATABASE__SSL_KEY_PATH=/path/to/client-key.pem

# ==================== JWT安全配置 ====================
# JWT密钥，请生成一个随机的长字符串（建议64位以上）
# 可以使用以下命令生成：openssl rand -hex 32
LOVESTORY_JWT__SECRET_KEY=PLEASE_GENERATE_A_RANDOM_SECRET_KEY_HERE
LOVESTORY_JWT__ALGORITHM=HS256
# JWT令牌有效期（分钟），默认24小时
LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ==================== 管理员配置 ====================
# 初始管理员用户名
LOVESTORY_ADMIN__INITIAL_USER=admin
# 初始管理员密码，留空则自动生成随机密码
# LOVESTORY_ADMIN__INITIAL_PASSWORD=your_admin_password

# ==================== 第三方API配置 ====================
# Bangumi OAuth配置（可选）
# 如需使用Bangumi数据源，请在 https://bgm.tv/dev/app 申请
# LOVESTORY_BANGUMI__CLIENT_ID=your_bangumi_client_id
# LOVESTORY_BANGUMI__CLIENT_SECRET=your_bangumi_client_secret

# 豆瓣配置（可选）
# 如需使用豆瓣数据源，请提供有效的Cookie
# LOVESTORY_DOUBAN__COOKIE=your_douban_cookie

# ==================== 其他配置 ====================
# 时区设置
TZ=Asia/Shanghai

# Python环境配置
PYTHONPATH=/www/wwwroot/danmu_server
PYTHONUNBUFFERED=1