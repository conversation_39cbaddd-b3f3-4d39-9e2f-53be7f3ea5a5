# LoveStory弹幕服务环境变量配置示例
# 复制此文件为 .env 并修改相应的值
# .env 文件不应提交到版本控制中

# ==================== 容器权限配置 ====================
# 设置运行容器的用户和组ID，避免权限问题
# Linux/macOS: 使用 `id -u` 和 `id -g` 获取
PUID=1000
PGID=1000
UMASK=0022

# ==================== 服务器配置 ====================
LOVESTORY_SERVER__HOST=0.0.0.0
LOVESTORY_SERVER__PORT=7768

# ==================== 数据库配置 ====================
# 选择一种数据库配置方式：

# 方式1: 本地MySQL配置
# LOVESTORY_DATABASE__HOST=localhost
# LOVESTORY_DATABASE__PORT=3306
# LOVESTORY_DATABASE__USER=danmu_user
# LOVESTORY_DATABASE__PASSWORD=change_me_to_a_strong_password
# LOVESTORY_DATABASE__NAME=danmu_db
# LOVESTORY_DATABASE__SSL_ENABLED=false

# 方式2: TiDB Cloud配置（示例）
LOVESTORY_DATABASE__HOST=your-tidb-host.tidbcloud.com
LOVESTORY_DATABASE__PORT=4000
LOVESTORY_DATABASE__USER=your-username
LOVESTORY_DATABASE__PASSWORD=your-password
LOVESTORY_DATABASE__NAME=your-database
LOVESTORY_DATABASE__SSL_ENABLED=true
LOVESTORY_DATABASE__SSL_VERIFY_CERT=true
LOVESTORY_DATABASE__SSL_CA_PATH=/etc/pki/tls/certs/ca-bundle.crt

# ==================== JWT配置 ====================
# 使用 'openssl rand -base64 32' 生成强密钥
LOVESTORY_JWT__SECRET_KEY=change_me_to_a_strong_jwt_secret
LOVESTORY_JWT__ALGORITHM=HS256
LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ==================== 管理员配置 ====================
LOVESTORY_ADMIN__INITIAL_USER=admin
# LOVESTORY_ADMIN__INITIAL_PASSWORD=your_admin_password  # 如果不设置，将生成随机密码

# ==================== Bangumi OAuth配置 ====================
LOVESTORY_BANGUMI__CLIENT_ID=bgm4222688b7532ef439
LOVESTORY_BANGUMI__CLIENT_SECRET=379c426b8f26b561642334445761361f

# ==================== 豆瓣配置 ====================
# LOVESTORY_DOUBAN__COOKIE=your_douban_cookie

# ==================== 旧版MySQL配置（兼容性） ====================
# 如果使用独立的MySQL容器，可以设置这些变量
MYSQL_DATABASE=danmu_db
MYSQL_USER=danmu_user
MYSQL_PASSWORD=change_me_to_a_strong_password
MYSQL_ROOT_PASSWORD=change_me_to_a_strong_root_password