#!/bin/bash

# LoveStory弹幕服务启动脚本（宝塔面板版）
# 
# 使用说明：
# 1. 确保已安装Python 3.8+和pip
# 2. 确保已配置MySQL数据库
# 3. 复制.env.example为.env并配置
# 4. 运行此脚本启动服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo -e "${BLUE}🚀 LoveStory弹幕服务启动脚本${NC}"
echo -e "${BLUE}📁 工作目录: $SCRIPT_DIR${NC}"
echo ""

# 检查Python环境
check_python() {
    echo -e "${YELLOW}🔍 检查Python环境...${NC}"
    
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3未安装，请先安装Python 3.8+${NC}"
        echo "宝塔面板安装方法：软件商店 -> 运行环境 -> Python项目管理器"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    echo -e "${GREEN}✅ Python版本: $PYTHON_VERSION${NC}"
    
    if ! command -v pip3 &> /dev/null; then
        echo -e "${RED}❌ pip3未安装${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ pip3已安装${NC}"
}

# 检查环境配置
check_config() {
    echo -e "${YELLOW}🔍 检查配置文件...${NC}"
    
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}⚠️  未找到.env文件${NC}"
        if [ -f ".env.example" ]; then
            echo -e "${YELLOW}📋 复制.env.example为.env...${NC}"
            cp .env.example .env
            echo -e "${RED}❗ 请编辑.env文件配置数据库等信息后重新运行此脚本${NC}"
            echo "编辑命令: nano .env 或 vim .env"
            exit 1
        else
            echo -e "${RED}❌ .env.example文件不存在${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ 找到.env配置文件${NC}"
    fi
    
    if [ ! -f "config/config.yml" ]; then
        echo -e "${RED}❌ config/config.yml文件不存在${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 找到config.yml配置文件${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}📦 安装Python依赖...${NC}"
    
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ requirements.txt文件不存在${NC}"
        exit 1
    fi
    
    # 升级pip
    python3 -m pip install --upgrade pip
    
    # 安装依赖
    python3 -m pip install -r requirements.txt
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 检查数据库连接
check_database() {
    echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
    
    # 从.env文件读取数据库配置
    if [ -f ".env" ]; then
        source .env
    fi
    
    # 检查MySQL是否运行
    if command -v systemctl &> /dev/null; then
        if systemctl is-active --quiet mysql; then
            echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
        else
            echo -e "${YELLOW}⚠️  MySQL服务未运行，尝试启动...${NC}"
            sudo systemctl start mysql || echo -e "${YELLOW}⚠️  无法启动MySQL，请在宝塔面板中检查MySQL状态${NC}"
        fi
    fi
    
    echo -e "${YELLOW}💡 请确保已在宝塔面板MySQL中创建数据库和用户${NC}"
}

# 创建必要目录
create_directories() {
    echo -e "${YELLOW}📁 创建必要目录...${NC}"
    
    mkdir -p config/logs
    mkdir -p data
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 启动服务
start_service() {
    echo -e "${YELLOW}🚀 启动LoveStory弹幕服务...${NC}"
    
    # 设置环境变量
    export PYTHONPATH="$SCRIPT_DIR"
    export PYTHONUNBUFFERED=1
    
    # 启动服务
    echo -e "${BLUE}📝 启动命令: python3 -m src.main${NC}"
    echo -e "${BLUE}📊 服务将在端口7768上运行${NC}"
    echo -e "${BLUE}🌐 访问地址: http://localhost:7768${NC}"
    echo ""
    
    # 执行启动命令
    exec python3 -m src.main
}

# 主函数
main() {
    # 解析命令行参数
    SKIP_DEPS=false
    SKIP_DB_CHECK=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-db-check)
                SKIP_DB_CHECK=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-deps       跳过依赖安装"
                echo "  --skip-db-check   跳过数据库检查"
                echo "  -h, --help        显示帮助信息"
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                exit 1
                ;;
        esac
    done
    
    check_python
    check_config
    
    if [ "$SKIP_DEPS" != "true" ]; then
        install_dependencies
    fi
    
    if [ "$SKIP_DB_CHECK" != "true" ]; then
        check_database
    fi
    
    create_directories
    start_service
}

# 运行主函数
main "$@"
