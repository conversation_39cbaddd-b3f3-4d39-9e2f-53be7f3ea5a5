#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LoveStory弹幕服务简单启动脚本（宝塔面板版）

这是一个简化的Python启动脚本，适用于宝塔面板的Python项目管理器。
可以直接在宝塔面板中配置此脚本作为启动文件。

使用方法：
1. 在宝塔面板Python项目管理器中创建项目
2. 设置启动文件为此脚本
3. 配置环境变量（可选）
4. 启动项目
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主启动函数"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    os.chdir(project_root)
    
    print(f"🚀 LoveStory弹幕服务启动中...")
    print(f"📁 项目目录: {project_root}")
    
    # 设置Python路径
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(project_root)
    os.environ['PYTHONUNBUFFERED'] = '1'
    
    # 检查必要文件
    required_files = [
        'src/main.py',
        'config/config.yml',
        'requirements.txt'
    ]
    
    for file_path in required_files:
        if not (project_root / file_path).exists():
            print(f"❌ 缺少必要文件: {file_path}")
            sys.exit(1)
    
    print("✅ 必要文件检查通过")
    
    # 检查.env文件
    env_file = project_root / '.env'
    env_example = project_root / '.env.example'
    
    if not env_file.exists() and env_example.exists():
        print("⚠️  未找到.env文件，复制.env.example...")
        import shutil
        shutil.copy2(env_example, env_file)
        print("❗ 请编辑.env文件配置数据库等信息")
    
    # 创建必要目录
    (project_root / 'config' / 'logs').mkdir(parents=True, exist_ok=True)
    (project_root / 'data').mkdir(parents=True, exist_ok=True)
    
    print("📁 目录结构检查完成")
    
    # 加载环境变量
    if env_file.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file)
            print("✅ 环境变量加载完成")
        except ImportError:
            print("⚠️  python-dotenv未安装，跳过.env文件加载")
    
    # 启动应用
    try:
        print("🌟 启动LoveStory弹幕服务...")
        print("🌐 服务将在 http://localhost:7768 上运行")
        print("📚 API文档: http://localhost:7768/docs")
        print("-" * 50)
        
        # 导入并运行主应用
        from src.main import app
        import uvicorn
        from src.config import settings
        
        # 启动服务
        uvicorn.run(
            app,
            host=settings.server.host,
            port=settings.server.port,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
