#!/bin/bash

# LoveStory弹幕服务运行脚本（宝塔面板版）
# 适用于宝塔面板进程守护功能

set -e

# 获取脚本所在目录并切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 LoveStory弹幕服务启动中..."
echo "📁 工作目录: $SCRIPT_DIR"

# 设置Python环境变量
export PYTHONPATH="$SCRIPT_DIR"
export PYTHONUNBUFFERED=1

# 检查.env文件
if [ ! -f ".env" ] && [ -f ".env.example" ]; then
    echo "⚠️  复制.env.example为.env，请配置后重启"
    cp .env.example .env
fi

# 创建必要目录
mkdir -p config/logs
mkdir -p data

echo "🌟 正在启动主程序: python3 -m src.main"
echo "🌐 服务将在端口7768上运行"

# 使用exec替换当前进程，确保信号处理正确
exec python3 -m src.main