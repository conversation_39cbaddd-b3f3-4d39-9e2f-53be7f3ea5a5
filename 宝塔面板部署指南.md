# LoveStory弹幕服务 - 宝塔面板部署指南

## 📋 概述

本指南将帮助您在宝塔面板上部署LoveStory弹幕服务，无需使用Docker容器，直接在服务器上运行。

## 🔧 系统要求

- **操作系统**: Linux (CentOS 7+, Ubuntu 18.04+, Debian 9+)
- **宝塔面板**: 7.0+ 版本
- **Python**: 3.8.0+ 版本（推荐3.8.0-3.11.x，已针对Python 3.8兼容性优化）
- **MySQL**: 5.7+ 或 8.0+ 版本
- **内存**: 建议 2GB+
- **存储**: 建议 10GB+ 可用空间

### Python版本说明

本项目已针对Python 3.8.0进行兼容性优化：
- ✅ **支持**: Python 3.8.0 - 3.11.x
- ⚠️ **可能兼容**: Python 3.12.x+（可能存在依赖兼容性问题）
- ❌ **不支持**: Python 3.7.x及以下版本

## 📦 准备工作

### 1. 安装宝塔面板

如果还未安装宝塔面板，请参考官方文档：https://www.bt.cn/new/download.html

```bash
# CentOS/RHEL
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2. 在宝塔面板中安装必要软件

登录宝塔面板后，在"软件商店"中安装：

- **MySQL 8.0** (或 5.7)
- **Python项目管理器**
- **Nginx** (可选，用于反向代理)

## 🗄️ 数据库配置

### 1. 创建数据库

在宝塔面板的"数据库"页面：

1. 点击"添加数据库"
2. 数据库名：`danmu_db`
3. 用户名：`danmu_user`
4. 密码：设置一个强密码（建议16位以上）
5. 访问权限：选择"本地服务器"

### 2. 记录数据库信息

请记录以下信息，稍后配置时需要：
- 数据库主机：`127.0.0.1`
- 数据库端口：`3306`
- 数据库名：`danmu_db`
- 用户名：`danmu_user`
- 密码：您设置的密码

## 📁 项目部署

### 1. 上传项目文件

1. 在宝塔面板"文件"页面，进入 `/www/wwwroot/`
2. 创建新目录：`danmu_server`
3. 将项目文件上传到 `/www/wwwroot/danmu_server/`

或者使用命令行：

```bash
cd /www/wwwroot/
git clone <项目地址> danmu_server
cd danmu_server
```

### 2. 配置环境变量

复制环境变量模板：

```bash
cd /www/wwwroot/danmu_server
cp .env.example .env
```

编辑 `.env` 文件：

```bash
nano .env
```

修改以下关键配置：

```env
# 数据库配置
LOVESTORY_DATABASE__HOST=127.0.0.1
LOVESTORY_DATABASE__PORT=3306
LOVESTORY_DATABASE__USER=danmu_user
LOVESTORY_DATABASE__PASSWORD=您的数据库密码
LOVESTORY_DATABASE__NAME=danmu_db

# JWT密钥（请生成随机字符串）
LOVESTORY_JWT__SECRET_KEY=您的随机密钥

# 管理员配置
LOVESTORY_ADMIN__INITIAL_USER=admin
```

**生成JWT密钥的方法：**

```bash
# 方法1：使用openssl
openssl rand -hex 32

# 方法2：使用Python
python3 -c "import secrets; print(secrets.token_hex(32))"
```

### 3. 检查Python兼容性

在安装依赖前，建议先检查Python环境：

```bash
cd /www/wwwroot/danmu_server
python3 check_python_compatibility.py
```

### 4. 安装Python依赖

在宝塔面板"Python项目管理器"中：

1. 点击"添加Python项目"
2. 项目名称：`LoveStory弹幕服务`
3. 项目路径：`/www/wwwroot/danmu_server`
4. Python版本：选择 3.8+ 版本（推荐3.8.0-3.11.x）
5. 启动文件：`start_simple.py`
6. 端口：`7768`

或者使用命令行安装依赖：

```bash
cd /www/wwwroot/danmu_server
# 检查Python版本
python3 --version
# 安装依赖
pip3 install -r requirements.txt
```

**注意**: 如果遇到依赖安装问题，可能是Python版本不兼容，请确保使用Python 3.8.0+版本。

## 🚀 启动服务

### 方法1：使用宝塔面板Python项目管理器（推荐）

1. 在"Python项目管理器"中找到您的项目
2. 点击"设置"
3. 确认启动文件为 `start_simple.py`
4. 点击"启动"

### 方法2：使用进程守护

1. 在宝塔面板"软件商店"安装"进程守护管理器"
2. 添加守护进程：
   - 名称：`LoveStory弹幕服务`
   - 启动文件：`/www/wwwroot/danmu_server/run.sh`
   - 运行目录：`/www/wwwroot/danmu_server`

### 方法3：使用启动脚本

```bash
cd /www/wwwroot/danmu_server
chmod +x start.sh
./start.sh
```

## 🌐 访问配置

### 1. 防火墙设置

在宝塔面板"安全"页面，添加端口规则：
- 端口：`7768`
- 协议：`TCP`
- 策略：`放行`

### 2. 访问服务

- **Web管理界面**: `http://您的服务器IP:7768`
- **API文档**: `http://您的服务器IP:7768/docs`

### 3. 首次登录

- 用户名：`admin`
- 密码：查看启动日志获取自动生成的密码

查看密码的方法：

```bash
# 如果使用Python项目管理器
在宝塔面板中查看项目日志

# 如果使用命令行启动
tail -f /www/wwwroot/danmu_server/config/logs/app.log
```

## 🔧 Nginx反向代理（可选）

如果希望使用域名访问，可以配置Nginx反向代理：

1. 在宝塔面板"网站"页面添加站点
2. 在站点设置中添加反向代理：

```nginx
location / {
    proxy_pass http://127.0.0.1:7768;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 📊 服务管理

### 查看服务状态

```bash
# 查看进程
ps aux | grep python | grep src.main

# 查看端口占用
netstat -tlnp | grep 7768
```

### 重启服务

```bash
# 如果使用Python项目管理器
在宝塔面板中重启项目

# 如果使用进程守护
在进程守护管理器中重启

# 如果使用命令行
pkill -f "python.*src.main"
cd /www/wwwroot/danmu_server && ./start.sh
```

### 查看日志

```bash
# 应用日志
tail -f /www/wwwroot/danmu_server/config/logs/app.log

# 如果使用Python项目管理器
在宝塔面板中查看项目日志
```

## 🔒 安全建议

1. **修改默认密码**: 首次登录后立即修改管理员密码
2. **数据库安全**: 使用强密码，限制数据库访问权限
3. **防火墙配置**: 只开放必要的端口
4. **定期备份**: 定期备份数据库和配置文件
5. **SSL证书**: 生产环境建议配置HTTPS

## ❓ 常见问题

### Q: 服务启动失败怎么办？

A: 检查以下几点：
1. Python版本是否为3.8+
2. 依赖是否正确安装
3. 数据库连接是否正常
4. 端口是否被占用
5. 查看错误日志

### Q: 无法访问Web界面？

A: 检查：
1. 防火墙是否开放7768端口
2. 服务是否正常运行
3. 服务器IP地址是否正确

### Q: 数据库连接失败？

A: 检查：
1. MySQL服务是否运行
2. 数据库用户名密码是否正确
3. 数据库是否存在
4. .env文件配置是否正确

### Q: 如何配置第三方API？

A: 在Web管理界面的"设置"页面配置：
1. Bangumi API：用于获取番剧信息
2. TMDB API：用于获取电影电视剧信息
3. 豆瓣Cookie：用于获取豆瓣评分信息

### Q: 如何备份数据？

A: 定期备份以下内容：
1. 数据库：在宝塔面板数据库页面导出
2. 配置文件：备份 `.env` 和 `config/` 目录
3. 日志文件：备份 `config/logs/` 目录

## 🔄 更新升级

### 更新应用

1. 停止服务
2. 备份当前版本
3. 下载新版本文件
4. 覆盖应用文件（保留配置）
5. 重启服务

```bash
# 停止服务
在宝塔面板中停止Python项目

# 备份
cp -r /www/wwwroot/danmu_server /www/backup/danmu_server_$(date +%Y%m%d)

# 更新代码（保留配置文件）
cd /www/wwwroot/danmu_server
git pull  # 或手动上传新文件

# 重启服务
在宝塔面板中启动Python项目
```

## 📈 性能优化

### 1. 数据库优化

在宝塔面板MySQL配置中调整：

```ini
# my.cnf 优化建议
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
```

### 2. 应用优化

- 定期清理日志文件
- 监控内存使用情况
- 配置适当的并发数

### 3. 系统监控

在宝塔面板"监控"页面关注：
- CPU使用率
- 内存使用率
- 磁盘空间
- 网络流量

## 🛡️ 高级安全配置

### 1. 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER 'danmu_user'@'127.0.0.1' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, INDEX ON danmu_db.* TO 'danmu_user'@'127.0.0.1';
FLUSH PRIVILEGES;
```

### 2. 文件权限

```bash
# 设置适当的文件权限
chown -R www:www /www/wwwroot/danmu_server
chmod -R 755 /www/wwwroot/danmu_server
chmod 600 /www/wwwroot/danmu_server/.env
```

### 3. 防火墙规则

```bash
# 只允许特定IP访问（可选）
iptables -A INPUT -p tcp --dport 7768 -s 允许的IP地址 -j ACCEPT
iptables -A INPUT -p tcp --dport 7768 -j DROP
```

## 📞 技术支持

如遇到问题，请：
1. 查看应用日志：`/www/wwwroot/danmu_server/config/logs/`
2. 检查宝塔面板系统日志
3. 参考项目文档和配置文件注释
4. 提交Issue到项目仓库并提供详细错误信息

## 📋 部署检查清单

部署完成后，请确认以下项目：

- [ ] MySQL服务正常运行
- [ ] 数据库和用户创建成功
- [ ] Python依赖安装完成
- [ ] .env文件配置正确
- [ ] 防火墙端口开放
- [ ] 服务正常启动
- [ ] Web界面可以访问
- [ ] 管理员账户登录成功
- [ ] 数据库连接正常
- [ ] 日志文件正常生成

---

**祝您部署顺利！🎉**

> 💡 **提示**: 建议在测试环境先完整部署一遍，确认无误后再在生产环境部署。
