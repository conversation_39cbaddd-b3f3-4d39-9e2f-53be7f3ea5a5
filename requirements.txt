# LoveStory弹幕服务依赖包 - Python 3.8+ 兼容版本

# Web框架和服务器
fastapi>=0.68.0,<1.0.0
uvicorn[standard]>=0.15.0,<1.0.0

# 数据库
aiomysql>=0.1.1,<1.0.0

# 任务调度
apscheduler>=3.9.0,<4.0.0

# 配置管理 - 确保与Python 3.8兼容
pydantic>=1.10.0,<2.0.0
pydantic-settings>=1.0.0,<2.0.0

# HTTP客户端
httpx>=0.24.0,<1.0.0

# 密码加密 - 使用固定版本避免兼容性问题
# passlib>=1.7.4 才与 bcrypt>=4.0 兼容
passlib>=1.7.4,<2.0.0
bcrypt==4.0.1

# JWT处理
python-jose[cryptography]>=3.3.0,<4.0.0

# 文件上传支持
python-multipart>=0.0.5,<1.0.0

# protobuf - 固定到v3.x避免兼容性问题
# v4.x 引入了不兼容的变更，可能导致预编译的 _pb2.py 文件解析失败
protobuf==3.20.3

# 字符串匹配和处理
thefuzz>=0.19.0,<1.0.0
python-Levenshtein>=0.12.0,<1.0.0

# HTML解析
beautifulsoup4>=4.9.0,<5.0.0
lxml>=4.6.0,<5.0.0

# 中文处理
opencc-python-reimplemented>=1.1.0,<2.0.0

# YAML配置文件支持
PyYAML>=5.4.0,<7.0.0