# LoveStory弹幕服务 - 宝塔面板部署版

## 🎯 简介

这是LoveStory弹幕服务的宝塔面板部署版本，已移除Docker相关文件，专为直接在服务器上部署而优化。

## 📁 文件说明

### 核心文件
- `src/` - 应用源代码目录
- `config/config.yml` - 主配置文件（已优化为宝塔部署）
- `requirements.txt` - Python依赖列表
- `static/` - 静态文件目录

### 配置文件
- `.env.example` - 环境变量模板（已针对宝塔面板优化）
- `config/config.yml` - 应用配置文件

### 启动脚本
- `start.sh` - 完整启动脚本（包含环境检查）
- `start_simple.py` - 简单Python启动脚本（适用于宝塔Python项目管理器）
- `run.sh` - 基础运行脚本（适用于进程守护）

### 部署工具
- `install.sh` - 一键安装脚本
- `宝塔面板部署指南.md` - 详细部署文档

## 🚀 快速部署

### 方法1：一键安装（推荐）

```bash
# 下载项目文件到服务器
cd /www/wwwroot/
git clone <项目地址> danmu_server
cd danmu_server

# 运行一键安装脚本
chmod +x install.sh
./install.sh
```

### 方法2：手动部署

1. **准备环境**
   - 安装宝塔面板
   - 安装MySQL 8.0
   - 安装Python项目管理器

2. **配置数据库**
   ```sql
   CREATE DATABASE danmu_db CHARACTER SET utf8mb4;
   CREATE USER 'danmu_user'@'127.0.0.1' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON danmu_db.* TO 'danmu_user'@'127.0.0.1';
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   nano .env  # 编辑配置
   ```

4. **安装依赖**
   ```bash
   pip3 install -r requirements.txt
   ```

5. **启动服务**
   ```bash
   ./start.sh
   ```

## 🔧 启动方式

### 1. 宝塔Python项目管理器（推荐）
- 启动文件：`start_simple.py`
- 端口：`7768`
- 自动管理进程生命周期

### 2. 宝塔进程守护管理器
- 启动文件：`/www/wwwroot/danmu_server/run.sh`
- 运行目录：`/www/wwwroot/danmu_server`

### 3. 系统服务（systemd）
```bash
# 使用install.sh会自动创建系统服务
systemctl start lovestory-danmu.service
systemctl enable lovestory-danmu.service
```

### 4. 手动启动
```bash
cd /www/wwwroot/danmu_server
./start.sh
```

## 📊 访问信息

- **Web管理界面**: `http://服务器IP:7768`
- **API文档**: `http://服务器IP:7768/docs`
- **默认管理员**: `admin`（密码在启动日志中）

## 🔒 安全配置

1. **防火墙设置**
   - 在宝塔面板安全页面开放7768端口

2. **数据库安全**
   - 使用强密码
   - 限制数据库访问权限

3. **应用安全**
   - 修改默认JWT密钥
   - 首次登录后修改管理员密码

## 📝 配置说明

### 环境变量优先级
1. 环境变量（最高）
2. `.env` 文件
3. `config/config.yml`（最低）

### 关键配置项
```env
# 数据库配置
LOVESTORY_DATABASE__HOST=127.0.0.1
LOVESTORY_DATABASE__USER=danmu_user
LOVESTORY_DATABASE__PASSWORD=your_password
LOVESTORY_DATABASE__NAME=danmu_db

# JWT安全密钥
LOVESTORY_JWT__SECRET_KEY=your_random_secret

# 服务端口
LOVESTORY_SERVER__PORT=7768
```

## 🛠️ 管理命令

### 服务管理
```bash
# 查看服务状态
systemctl status lovestory-danmu.service

# 重启服务
systemctl restart lovestory-danmu.service

# 查看日志
journalctl -u lovestory-danmu.service -f
```

### 应用管理
```bash
# 查看应用日志
tail -f /www/wwwroot/danmu_server/config/logs/app.log

# 查看进程
ps aux | grep "python.*src.main"

# 查看端口占用
netstat -tlnp | grep 7768
```

## 🔄 更新升级

```bash
# 停止服务
systemctl stop lovestory-danmu.service

# 备份当前版本
cp -r /www/wwwroot/danmu_server /www/backup/danmu_server_$(date +%Y%m%d)

# 更新代码
cd /www/wwwroot/danmu_server
git pull

# 重启服务
systemctl start lovestory-danmu.service
```

## ❓ 故障排除

### 常见问题
1. **服务启动失败**
   - 检查Python版本（需要3.8+）
   - 检查依赖是否安装完整
   - 查看错误日志

2. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证数据库用户名密码
   - 确认数据库存在

3. **端口访问失败**
   - 检查防火墙设置
   - 确认服务正在运行
   - 验证端口未被占用

### 日志位置
- 应用日志：`/www/wwwroot/danmu_server/config/logs/`
- 系统日志：`journalctl -u lovestory-danmu.service`
- 宝塔日志：宝塔面板 -> 日志

## 📞 技术支持

如需帮助，请：
1. 查看详细部署指南：`宝塔面板部署指南.md`
2. 检查应用和系统日志
3. 提交Issue并提供错误信息

---

**部署愉快！🎉**
