#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python兼容性检查脚本

检查当前Python环境是否满足LoveStory弹幕服务的运行要求。
支持Python 3.8.0+版本。
"""

import sys
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    
    version_info = sys.version_info
    version_str = f"{version_info.major}.{version_info.minor}.{version_info.micro}"
    
    print(f"当前Python版本: {version_str}")
    
    # 检查最低版本要求 (3.8.0)
    if version_info < (3, 8, 0):
        print("❌ Python版本过低！")
        print("最低要求: Python 3.8.0")
        print("请升级Python版本")
        return False
    
    # 检查是否为推荐版本
    if version_info >= (3, 8, 0) and version_info < (3, 12, 0):
        print("✅ Python版本兼容")
    elif version_info >= (3, 12, 0):
        print("⚠️  Python版本较新，可能存在兼容性问题")
        print("推荐版本: Python 3.8.0 - 3.11.x")
    
    return True

def check_required_modules():
    """检查必需的Python模块"""
    print("\n🔍 检查必需的Python模块...")
    
    required_modules = [
        ('asyncio', '异步IO支持'),
        ('pathlib', '路径处理'),
        ('typing', '类型注解'),
        ('json', 'JSON处理'),
        ('yaml', 'YAML配置文件支持'),
    ]
    
    missing_modules = []
    
    for module_name, description in required_modules:
        try:
            if module_name == 'yaml':
                # PyYAML模块的导入名是yaml
                import yaml
            else:
                importlib.import_module(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError:
            print(f"❌ {module_name} - {description} (缺失)")
            missing_modules.append(module_name)
    
    if missing_modules:
        print(f"\n❌ 缺失必需模块: {', '.join(missing_modules)}")
        return False
    
    return True

def check_optional_dependencies():
    """检查可选依赖"""
    print("\n🔍 检查可选依赖...")
    
    optional_deps = [
        ('fastapi', 'Web框架'),
        ('uvicorn', 'ASGI服务器'),
        ('aiomysql', 'MySQL异步驱动'),
        ('pydantic', '数据验证'),
        ('httpx', 'HTTP客户端'),
        ('apscheduler', '任务调度'),
        ('passlib', '密码加密'),
        ('jose', 'JWT处理'),
        ('multipart', '文件上传'),
        ('bs4', 'HTML解析'),
        ('lxml', 'XML解析'),
        ('opencc', '中文转换'),
        ('thefuzz', '字符串匹配'),
    ]
    
    available_deps = []
    missing_deps = []
    
    for module_name, description in optional_deps:
        try:
            if module_name == 'jose':
                from jose import jwt
            elif module_name == 'multipart':
                import multipart
            elif module_name == 'bs4':
                import bs4
            elif module_name == 'opencc':
                import opencc
            else:
                importlib.import_module(module_name)
            print(f"✅ {module_name} - {description}")
            available_deps.append(module_name)
        except ImportError:
            print(f"⚠️  {module_name} - {description} (未安装)")
            missing_deps.append(module_name)
    
    if missing_deps:
        print(f"\n💡 可通过以下命令安装缺失依赖:")
        print("pip3 install -r requirements.txt")
    
    return len(available_deps) > len(missing_deps)

def check_syntax_compatibility():
    """检查语法兼容性"""
    print("\n🔍 检查语法兼容性...")
    
    # 检查是否支持类型注解
    try:
        from typing import Optional, Dict, List, Union
        print("✅ 类型注解支持")
    except ImportError:
        print("❌ 类型注解不支持")
        return False
    
    # 检查是否支持f-string
    try:
        test_var = "test"
        test_fstring = f"This is a {test_var}"
        print("✅ f-string支持")
    except SyntaxError:
        print("❌ f-string不支持")
        return False
    
    # 检查是否支持async/await
    try:
        async def test_async():
            pass
        print("✅ async/await支持")
    except SyntaxError:
        print("❌ async/await不支持")
        return False
    
    # 检查pathlib支持
    try:
        from pathlib import Path
        test_path = Path(".")
        print("✅ pathlib支持")
    except ImportError:
        print("❌ pathlib不支持")
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("\n🔍 检查项目结构...")
    
    required_files = [
        'src/main.py',
        'src/config.py',
        'config/config.yml',
        'requirements.txt',
        '.env.example'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺失必需文件: {', '.join(missing_files)}")
        return False
    
    return True

def main():
    """主检查函数"""
    print("🚀 LoveStory弹幕服务 - Python兼容性检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("必需模块", check_required_modules),
        ("语法兼容性", check_syntax_compatibility),
        ("项目结构", check_project_structure),
        ("可选依赖", check_optional_dependencies),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有检查通过！您的环境已准备就绪。")
        print("可以运行以下命令启动服务:")
        print("  python3 -m src.main")
        return True
    elif passed >= total - 1:  # 允许可选依赖检查失败
        print("\n⚠️  大部分检查通过，可以尝试运行服务。")
        print("如果遇到问题，请安装缺失的依赖:")
        print("  pip3 install -r requirements.txt")
        return True
    else:
        print("\n❌ 检查失败，请解决上述问题后重试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
